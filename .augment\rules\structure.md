---
type: "always_apply"
---

# Project Structure

## Service Organization Pattern

Each service follows a consistent 4-layer architecture:

```
Service.[Domain].[Component].[Layer]/
├── Models/           # Data transfer objects and entities
├── Store/           # Data access layer (repositories, MongoDB clients)
├── System/          # Business logic layer (services, orchestrators)
└── Serverless/      # AWS Lambda functions and handlers
```

## Naming Conventions

### Projects

- **Service.[Domain].[Component].[Layer]** - New services (e.g., `Service.Notifications.Email.Store`)
- **CM.[Domain].[Component]** - Legacy construction management services (e.g., `CM.Accounts.Api`)

### Folders

- **Service.[Domain]** - Groups related service components
- **amtek-shared** - Shared utility libraries (git submodule)
- **Build** - Deployment scripts and build configurations

## Key Service Domains

- **Notifications** - Email orchestration, templates, routing
- **Accounts** - User management, settings, authentication
- **Projects** - Project components, user assignments, cost codes
- **BidOps/Advertisements** - Bid opportunities, documents, planholders
- **Reports** - Various reporting services (daily logs, time cards, etc.)
- **Approvals** - Workflow and approval processes
- **Equipment/Employees** - Resource management

## File Patterns

- **serverless.template** - CloudFormation template for AWS resources
- **config-[service]-[env].json** - Environment-specific configuration
- **[Service]Module.cs** - Dependency injection configuration
- **[Service]System.cs** - Main system/business logic class
- **Functions.cs** - Lambda function entry points (in Serverless projects)

## Data Access Architecture

**CRITICAL: Services never work directly with MongoDB - always use repositories**

### Layer Responsibilities:

- **Store Layer (Repositories)**:

  - Work directly with MongoClient via `Amtek.v8.Mongo`
  - Handle all MongoDB CRUD operations using **BsonDocument** (never `dynamic`)
  - Use PascalCase field names for all MongoDB document access
  - Provide strongly-typed data access methods
  - Abstract database implementation details

- **System Layer (Services)**:
  - Handle business logic and orchestration
  - Work with repositories to store and retrieve data
  - **NEVER access MongoDB directly**
  - Coordinate between multiple repositories when needed

```csharp
// Correct approach - Service uses Repository
public class NotificationService
{
    private readonly INotificationRepository _repository;

    public async Task ProcessNotification(Notification notification)
    {
        // Business logic here
        await _repository.SaveAsync(notification); // Use repository
    }
}

// Repository handles MongoDB directly using BsonDocument with PascalCase fields
public class NotificationRepository : INotificationRepository
{
    private readonly MongoClient _mongoClient;

    public async Task SaveAsync(Notification notification)
    {
        // Direct MongoDB operations using BsonDocument
        var document = new BsonDocument
        {
            ["NotificationId"] = notification.Id,
            ["Email"] = notification.Email,
            ["Status"] = notification.Status
        };
        await _collection.InsertOneAsync(document);
    }

    public async Task<Dictionary<string, object>> GetNotificationDataAsync(string id)
    {
        var doc = await _collection.Find(Builders<BsonDocument>.Filter.Eq("_id", ObjectId.Parse(id)))
            .FirstOrDefaultAsync();
        
        return new Dictionary<string, object>
        {
            ["email"] = doc["Email"]?.ToString() ?? "",
            ["status"] = doc["Status"]?.ToString() ?? ""
        };
    }
}
```

## Dependencies

- All services reference `amtek-shared` libraries for common functionality
- Store layers handle MongoDB operations via `Amtek.v8.Mongo`
- Serverless projects include CloudFormation templates for AWS resources
- System layers contain business logic and orchestration
- Cross-service communication via SNS/SQS messaging

## Build Scripts

- **ConstructionManagement.sln** - Main solution file with all projects
